"use client"

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'
import { Button } from '@/components/ui/button'
import { X, Maximize2, Minimize2 } from 'lucide-react'
import { cn } from '@/lib/utils'

// ✅ Fix SSR: Dynamic imports for browser-only xterm.js with Promise-based loading
let Terminal: any = null
let FitAddon: any = null
let WebLinksAddon: any = null
let xtermModulesLoaded = false
let xtermLoadingPromise: Promise<void> | null = null

// ✅ Fix SSR: Promise-based xterm.js module loading
const loadXtermModules = async (): Promise<void> => {
  if (xtermModulesLoaded) return
  if (xtermLoadingPromise) return xtermLoadingPromise

  xtermLoadingPromise = (async () => {
    try {
      console.log('🔄 [TerminalPanel] Loading xterm.js modules...')

      const [terminalModule, fitModule, webLinksModule] = await Promise.all([
        import('xterm'),
        import('xterm-addon-fit'),
        import('xterm-addon-web-links')
      ])

      // Load CSS separately to avoid import issues
      try {
        await import('xterm/css/xterm.css')
      } catch (cssError) {
        console.warn('⚠️ [TerminalPanel] Could not load xterm.css, continuing without it:', cssError)
      }

      Terminal = terminalModule.Terminal
      FitAddon = fitModule.FitAddon
      WebLinksAddon = webLinksModule.WebLinksAddon
      xtermModulesLoaded = true

      console.log('✅ [TerminalPanel] xterm.js modules loaded successfully')
    } catch (error) {
      console.error('❌ [TerminalPanel] Failed to load xterm.js modules:', error)
      throw error
    }
  })()

  return xtermLoadingPromise
}

// ✅ Start loading modules immediately on client side
if (typeof window !== 'undefined') {
  loadXtermModules().catch(console.error)
}

interface TerminalPanelProps {
  sessionId?: string
  shellType?: 'bash' | 'powershell' | 'cmd' | 'zsh'
  onClose?: () => void
  className?: string
  isFullscreen?: boolean
  onToggleFullscreen?: () => void
}

export default function TerminalPanel({
  sessionId,
  shellType = 'bash',
  onClose,
  className,
  isFullscreen = false,
  onToggleFullscreen
}: TerminalPanelProps) {
  const terminalRef = useRef<HTMLDivElement>(null)
  const terminalInstanceRef = useRef<any | null>(null)
  const fitAddonRef = useRef<any | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [backendSessionId, setBackendSessionId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [ptyError, setPtyError] = useState<string | null>(null)
  const [connectionAttempts, setConnectionAttempts] = useState(0)
  const { theme } = useTheme()
  const isDark = theme === 'dark'

  // ✅ Task 83: Terminal API access with enhanced debugging and retry mechanism
  const [terminalAPI, setTerminalAPI] = useState<any>(null)
  const [apiRetryCount, setApiRetryCount] = useState(0)

  // ✅ Task 86: Enhanced API detection with stable reference
  useEffect(() => {
    const checkAndSetAPI = () => {
      if (typeof window !== 'undefined') {
        const currentAPI = (window as any).electronAPI?.terminalAPI;

        if (currentAPI) {
          console.log('✅ [TerminalPanel] Terminal API detected and set!');
          setTerminalAPI(currentAPI);
          return true;
        }

        return false;
      }
      return false;
    };

    // Initial check
    if (!terminalAPI && checkAndSetAPI()) {
      return;
    }

    // Only retry if we don't have the API yet
    if (!terminalAPI) {
      const maxRetries = 10;
      const retryInterval = 500; // 500ms

      const retryTimer = setInterval(() => {
        setApiRetryCount(prev => {
          const newCount = prev + 1;
          console.log(`🔄 [TerminalPanel] API detection retry ${newCount}/${maxRetries}`);

          if (checkAndSetAPI() || newCount >= maxRetries) {
            clearInterval(retryTimer);
            if (newCount >= maxRetries && !terminalAPI) {
              console.error('❌ [TerminalPanel] API detection failed after maximum retries');
            }
          }

          return newCount;
        });
      }, retryInterval);

      return () => clearInterval(retryTimer);
    }
  }, [terminalAPI])

  // ✅ Task 83: Cleanup function for event listeners
  const cleanupRef = useRef<(() => void)[]>([])

  // ✅ Task 86: Use ref to track attempts to avoid dependency loop
  const attemptsRef = useRef(0)
  const initializingRef = useRef(false)

  // ✅ Task 86: Enhanced terminal initialization with comprehensive error handling
  const initializeTerminal = useCallback(async () => {
    // Prevent multiple simultaneous initializations
    if (initializingRef.current) {
      console.warn('⚠️ [TerminalPanel] Initialization already in progress, skipping...');
      return;
    }

    initializingRef.current = true;
    const startTime = Date.now();

    // Increment attempts using ref to avoid dependency loop
    attemptsRef.current += 1;
    setConnectionAttempts(attemptsRef.current);
    console.log(`🚀 [TerminalPanel] Starting terminal initialization (attempt ${attemptsRef.current})`);

    if (!terminalRef.current || !terminalAPI) {
      console.warn('⚠️ [TerminalPanel] Terminal ref or API not available');
      const debugInfo = {
        terminalRefExists: !!terminalRef.current,
        terminalAPIExists: !!terminalAPI,
        terminalAPIType: typeof terminalAPI,
        windowElectronAPI: typeof (window as any).electronAPI,
        windowTerminalAPI: typeof (window as any).electronAPI?.terminalAPI
      };
      console.warn('🔍 [TerminalPanel] Debug info:', JSON.stringify(debugInfo, null, 2));
      setError('Terminal API not available - ensure app is running in Electron');
      setIsLoading(false);
      initializingRef.current = false;
      return;
    }

    // ✅ Fix SSR: Wait for Promise-based module loading to complete
    try {
      await loadXtermModules()
      console.log('🔄 [TerminalPanel] xterm.js modules ready, proceeding with initialization...')
    } catch (error) {
      console.error('❌ [TerminalPanel] Failed to load xterm.js modules:', error)
      setError('Failed to load terminal components')
      setIsLoading(false)
      initializingRef.current = false
      return
    }

    // ✅ Keep loading state until terminal is fully connected
    // setIsLoading(false) moved to after successful connection

    try {
      // Create xterm.js terminal instance with production-ready configuration
      const terminal = new Terminal({
        theme: {
          background: isDark ? '#1e1e1e' : '#ffffff',
          foreground: isDark ? '#ffffff' : '#000000',
          cursor: isDark ? '#ffffff' : '#000000',
          selection: isDark ? '#ffffff40' : '#00000040',
          black: isDark ? '#000000' : '#000000',
          red: isDark ? '#ff6b6b' : '#d63031',
          green: isDark ? '#51cf66' : '#00b894',
          yellow: isDark ? '#ffd43b' : '#fdcb6e',
          blue: isDark ? '#74c0fc' : '#0984e3',
          magenta: isDark ? '#f06292' : '#e84393',
          cyan: isDark ? '#4dd0e1' : '#00cec9',
          white: isDark ? '#ffffff' : '#636e72'
        },
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        cursorBlink: true,
        cursorStyle: 'block',
        scrollback: 1000,
        tabStopWidth: 4,
        convertEol: true, // ✅ Task 83: Required for proper line ending handling
        allowTransparency: false,
        bellStyle: 'none'
      })

      // ✅ Task 83: Add required addons
      const fitAddon = new FitAddon()
      const webLinksAddon = new WebLinksAddon()

      terminal.loadAddon(fitAddon)
      terminal.loadAddon(webLinksAddon)

      // Store references
      terminalInstanceRef.current = terminal
      fitAddonRef.current = fitAddon

      // ✅ CRITICAL FIX: Ensure DOM element is properly mounted before opening terminal
      if (!terminalRef.current) {
        throw new Error('Terminal container ref is not available - DOM not mounted')
      }

      // ✅ Verify container has proper dimensions before opening
      const containerRect = terminalRef.current.getBoundingClientRect()
      console.log(`📐 [TerminalPanel] Container dimensions: ${containerRect.width}x${containerRect.height}`)

      if (containerRect.width === 0 || containerRect.height === 0) {
        console.warn('⚠️ [TerminalPanel] Container has zero dimensions, waiting for layout...')
        // Wait for container to have proper dimensions
        await new Promise(resolve => {
          const checkDimensions = () => {
            const rect = terminalRef.current?.getBoundingClientRect()
            if (rect && rect.width > 0 && rect.height > 0) {
              console.log(`✅ [TerminalPanel] Container ready: ${rect.width}x${rect.height}`)
              resolve(void 0)
            } else {
              setTimeout(checkDimensions, 50)
            }
          }
          checkDimensions()
        })
      }

      // ✅ CRITICAL FIX: Verify container is mounted and has tabIndex
      if (!terminalRef.current) {
        console.warn('[XTERM] Terminal container not mounted')
        return
      }

      console.log(`🔗 [TerminalPanel] Opening terminal in container...`)
      terminal.open(terminalRef.current)
      console.log(`✅ [TerminalPanel] Terminal opened successfully`)

      // ✅ IMMEDIATE FIT: Fit before any delays
      fitAddon.fit()
      console.log(`📏 [TerminalPanel] Terminal fitted: ${terminal.cols}x${terminal.rows}`)

      // ✅ REQUIRED: Focus after fit with proper timing
      setTimeout(() => {
        terminalRef.current?.focus()
        terminal.focus()
        console.log(`🎯 [TerminalPanel] Terminal focused after fit`)
      }, 100)

      // ✅ Task 86: Enhanced backend terminal session startup with detailed error handling
      console.log(`🔗 [TerminalPanel] Requesting PTY session from backend...`);
      console.log(`📊 [TerminalPanel] Terminal dimensions: ${terminal.cols}x${terminal.rows}`);

      const result = await terminalAPI.startTerminal(terminal.cols, terminal.rows, sessionId);
      const endTime = Date.now();

      console.log(`📡 [TerminalPanel] Backend response received in ${endTime - startTime}ms:`, result);

      if (!result.success) {
        console.error('❌ [TerminalPanel] PTY startup failed:', result.error);
        console.error('🔍 [TerminalPanel] Error details:', result.details);

        // ✅ Task 86: Set PTY-specific error for red banner
        const errorMessage = result.error || 'Failed to connect to terminal backend';
        const isNodePtyError = errorMessage.includes('node-pty') || errorMessage.includes('PTY');

        if (isNodePtyError) {
          setPtyError(`PTY Backend Error: ${errorMessage}`);
          setError(null); // Clear generic error
        } else {
          setError(errorMessage);
          setPtyError(null);
        }

        terminal.write('\r\n❌ Failed to connect to terminal backend\r\n');
        terminal.write(`Error: ${errorMessage}\r\n`);

        if (result.details) {
          terminal.write(`Platform: ${result.details.platform || 'unknown'}\r\n`);
          terminal.write(`Node: ${result.details.nodeVersion || 'unknown'}\r\n`);
          terminal.write(`Electron: ${result.details.electronVersion || 'unknown'}\r\n`);
        }

        return;
      }

      const backendId = result.sessionId;
      setBackendSessionId(backendId);
      setIsConnected(true);
      setPtyError(null); // Clear any previous PTY errors
      setError(null); // Clear any previous errors

      console.log(`✅ [TerminalPanel] PTY session established: ${backendId}`);
      console.log(`🔧 [TerminalPanel] Session details:`, result.details);

      // ✅ Task 83: Set up bidirectional communication

      // Handle data from backend PTY
      const dataCleanup = terminalAPI.onTerminalData((receivedSessionId: string, data: string) => {
        if (receivedSessionId === backendId && terminal) {
          terminal.write(data)
        }
      })

      // Handle terminal exit
      const exitCleanup = terminalAPI.onTerminalExit((receivedSessionId: string, exitCode: number) => {
        if (receivedSessionId === backendId) {
          console.log(`Terminal session ${backendId} exited with code ${exitCode}`)
          setIsConnected(false)

          // ✅ Provide more informative exit messages
          if (exitCode === 0) {
            terminal.write(`\r\n[Process completed successfully]\r\n`)
          } else {
            terminal.write(`\r\n[Process exited with code ${exitCode} - Session ended unexpectedly]\r\n`)
            terminal.write(`[Try typing a command or press Enter to restart]\r\n`)
          }
        }
      })

      // ✅ Handle terminal errors
      const errorCleanup = terminalAPI.onTerminalError ? terminalAPI.onTerminalError((receivedSessionId: string, error: string) => {
        if (receivedSessionId === backendId) {
          console.error(`Terminal session ${backendId} error:`, error)
          terminal.write(`\r\n[Terminal Error: ${error}]\r\n`)
          // Don't disconnect on errors, allow recovery
        }
      }) : () => {}

      // ✅ CRITICAL FIX: Enhanced input handling with proper validation
      const inputDisposable = terminal.onData((data: string) => {
        console.log(`🎯 [XTERM DATA] -> Input captured:`, JSON.stringify(data)) // ✅ This must log

        if (backendId && terminalAPI?.writeToTerminal) {
          console.log(`📤 [TerminalPanel] Sending input to backend session: ${backendId}`)
          terminalAPI.writeToTerminal(backendId, data)
          console.log(`✅ [TerminalPanel] Input sent successfully`)
        } else {
          console.warn(`⚠️ [TerminalPanel] Cannot send input - missing session or API`)
        }
      })

      // ✅ FORCE TERMINAL INTERACTION: Make terminal interactive immediately
      terminal.write('\r\n\x1b[32m✅ Terminal ready - type to test input\x1b[0m\r\n')
      terminal.focus()

      // ✅ FORCE FOCUS: Aggressive focus strategy
      const forceFocus = () => {
        if (terminal && terminalRef.current) {
          terminal.focus()
          terminalRef.current.focus()
        }
      }

      // Multiple focus attempts
      forceFocus()
      setTimeout(forceFocus, 100)
      setTimeout(forceFocus, 500)
      setTimeout(forceFocus, 1000)

      // Store cleanup functions
      cleanupRef.current = [
        dataCleanup,
        exitCleanup,
        errorCleanup,
        () => inputDisposable.dispose()
      ]

      // Focus terminal
      terminal.focus()

      // ✅ Terminal is now fully connected and ready for input
      setIsLoading(false)

      // ✅ Ensure terminal gets focus after loading state is cleared
      setTimeout(() => {
        if (terminal && terminalRef.current) {
          terminal.focus()
          console.log(`🎯 [TerminalPanel] Terminal focused after initialization`)
        }
      }, 100)

      // ✅ CRITICAL FIX: Add comprehensive debugging functions to window
      if (typeof window !== 'undefined') {
        // Manual input test
        (window as any).testTerminalInput = (testInput: string = 'echo "test"\n') => {
          console.log(`🧪 [TerminalPanel] Manual test input: ${JSON.stringify(testInput)}`)
          if (backendId && terminalAPI?.writeToTerminal) {
            terminalAPI.writeToTerminal(backendId, testInput)
            console.log(`✅ [TerminalPanel] Manual test sent successfully`)
          } else {
            console.error(`❌ [TerminalPanel] Manual test failed - no session or API`)
          }
        }

        // Advanced xterm debugging
        (window as any).testXterm = () => {
          console.log(`🧪 [TerminalPanel] Testing xterm directly...`)
          if (terminal) {
            terminal.write('echo test\r\n')
            terminal.focus()
            console.log(`✅ [TerminalPanel] Xterm test completed - check terminal output`)
          } else {
            console.error(`❌ [TerminalPanel] Terminal instance not available`)
          }
        }

        // Manual binding strategy (fallback)
        (window as any).manualTerminalBind = () => {
          console.log(`🔧 [TerminalPanel] Manual binding strategy...`)
          const container = document.getElementById('xterm-container')
          if (container && Terminal) {
            console.log(`📋 [TerminalPanel] Container found, creating manual terminal...`)
            const manualTerm = new Terminal()
            manualTerm.open(container)
            manualTerm.write('> manual terminal ready\n')
            manualTerm.onData(data => console.log('manual input:', data))
            manualTerm.focus()
            ;(window as any).manualTerm = manualTerm
            console.log(`✅ [TerminalPanel] Manual terminal created - try typing`)
          } else {
            console.error(`❌ [TerminalPanel] Container or Terminal class not available`)
          }
        }

        // Focus debugging
        (window as any).debugTerminalFocus = () => {
          console.log(`🔍 [TerminalPanel] Focus debug:`, {
            terminalInstance: !!terminal,
            terminalRef: !!terminalRef.current,
            activeElement: document.activeElement?.tagName,
            terminalHasFocus: terminal?.hasSelection(),
            containerRect: terminalRef.current?.getBoundingClientRect()
          })
          if (terminal && terminalRef.current) {
            terminal.focus()
            console.log(`🎯 [TerminalPanel] Focus forced`)
          }
        }

        console.log(`🧪 [TerminalPanel] Debug functions available:`)
        console.log(`  - window.testTerminalInput() - Test input flow`)
        console.log(`  - window.testXterm() - Test xterm directly`)
        console.log(`  - window.debugTerminalFocus() - Debug focus state`)
      }

    } catch (error) {
      console.error('❌ Error initializing terminal:', error)
      setError(error instanceof Error ? error.message : 'Failed to initialize terminal')
      setIsLoading(false)
    } finally {
      initializingRef.current = false;
    }
  }, [terminalAPI, sessionId, isDark])

  // ✅ Task 86: Reset function for retry buttons
  const resetAndRetry = useCallback(() => {
    attemptsRef.current = 0;
    setConnectionAttempts(0);
    setPtyError(null);
    setError(null);
    setIsLoading(true);
    initializeTerminal();
  }, [initializeTerminal])

  // ✅ Task 83: Handle window resize with dimension checks
  const handleResize = useCallback(() => {
    if (fitAddonRef.current && terminalInstanceRef.current && backendSessionId && terminalAPI && terminalRef.current) {
      // ✅ Check container dimensions before fitting
      const rect = terminalRef.current.getBoundingClientRect()
      if (rect.width > 0 && rect.height > 0) {
        try {
          fitAddonRef.current.fit()
          const { cols, rows } = terminalInstanceRef.current
          terminalAPI.resizeTerminal(backendSessionId, cols, rows)
        } catch (error) {
          console.warn('⚠️ [TerminalPanel] Resize failed:', error)
        }
      }
    }
  }, [backendSessionId, terminalAPI])

  // ✅ Task 83: Initialize terminal when both API and DOM ref are ready
  const hasInitialized = useRef(false)
  useEffect(() => {
    console.log('🔍 [TerminalPanel] Initialization check:', {
      terminalAPI: !!terminalAPI,
      terminalRef: !!terminalRef.current,
      hasInitialized: hasInitialized.current,
      initializingRef: initializingRef.current,
      terminalInstanceRef: !!terminalInstanceRef.current
    });

    // Only initialize when we have BOTH the API AND the DOM element
    if (terminalAPI && terminalRef.current && !hasInitialized.current && !initializingRef.current && !terminalInstanceRef.current) {
      hasInitialized.current = true
      console.log('🚀 [TerminalPanel] Starting terminal initialization...');
      initializeTerminal()
    } else if (!terminalAPI) {
      console.warn('⚠️ [TerminalPanel] Terminal API not available - cannot initialize terminal')
    } else if (!terminalRef.current) {
      console.log('⏳ [TerminalPanel] Waiting for DOM element to be ready...')
    } else {
      console.log('🔍 [TerminalPanel] Skipping initialization:', {
        reason: hasInitialized.current ? 'already initialized' :
                initializingRef.current ? 'currently initializing' :
                terminalInstanceRef.current ? 'instance exists' : 'unknown'
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [terminalAPI, terminalRef.current]) // Watch both API and DOM ref

  // ✅ Task 83: Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup on unmount
      cleanupRef.current.forEach(cleanup => cleanup())
      cleanupRef.current = []

      // Use current values from refs to avoid stale closure
      const currentSessionId = backendSessionId
      const currentTerminalAPI = terminalAPI
      const currentTerminalInstance = terminalInstanceRef.current

      if (currentSessionId && currentTerminalAPI) {
        currentTerminalAPI.closeTerminal(currentSessionId)
      }

      if (currentTerminalInstance) {
        currentTerminalInstance.dispose()
        terminalInstanceRef.current = null
      }
    }
  }, [backendSessionId, terminalAPI])

  // ✅ Task 83: Handle resize events
  useEffect(() => {
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [handleResize])

  // ✅ Task 83: Handle theme changes
  useEffect(() => {
    if (terminalInstanceRef.current) {
      terminalInstanceRef.current.options.theme = {
        background: isDark ? '#1e1e1e' : '#ffffff',
        foreground: isDark ? '#ffffff' : '#000000',
        cursor: isDark ? '#ffffff' : '#000000',
        selection: isDark ? '#ffffff40' : '#00000040'
      }
    }
  }, [isDark])

  return (
    <div className={cn('flex flex-col h-full bg-editor-terminal-bg', className)}>
      {/* ✅ Task 83: Terminal header with controls */}
      <div className="flex items-center justify-between h-8 px-2 border-b border-editor-border bg-editor-sidebar-bg">
        <div className="flex items-center space-x-2">
          <span className="text-xs text-muted-foreground">
            {shellType} {backendSessionId && `(${backendSessionId.slice(-8)})`}
          </span>
          <div className={cn(
            'w-2 h-2 rounded-full',
            isConnected ? 'bg-green-500' : ptyError ? 'bg-red-500' : 'bg-yellow-500'
          )} />
          {connectionAttempts > 1 && (
            <span className="text-xs text-yellow-600 dark:text-yellow-400">
              Attempt {connectionAttempts}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {onToggleFullscreen && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={onToggleFullscreen}
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
            </Button>
          )}

          {onClose && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={onClose}
              title="Close terminal"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* ✅ Task 86: PTY Error Banner */}
      {ptyError && (
        <div className="bg-red-500/10 border-l-4 border-red-500 px-3 py-2 text-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-red-500" />
              <span className="text-red-700 dark:text-red-300 font-medium">PTY Backend Failed</span>
            </div>
            <button
              onClick={resetAndRetry}
              className="text-xs px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Retry PTY
            </button>
          </div>
          <div className="text-xs text-red-600 dark:text-red-400 mt-1">
            {ptyError}
          </div>
          <div className="text-xs text-red-500/70 mt-1">
            Check that node-pty is properly installed and compiled for your platform
          </div>
        </div>
      )}

      {/* ✅ Task 83: Real xterm.js terminal container */}
      <div className="flex-1 relative overflow-hidden">
        {!terminalAPI ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center max-w-md">
              <div className="text-sm mb-2 text-red-500 font-medium">Terminal API Not Available</div>
              <div className="text-xs text-muted-foreground/70 mb-3">
                {navigator.userAgent.includes('Electron')
                  ? 'Electron detected but API not loaded. Check preload script.'
                  : 'Running in browser mode. Terminal requires Electron.'}
              </div>
              <div className="text-xs bg-blue-500/10 border border-blue-500/20 rounded p-2 mb-3">
                <div className="font-medium text-blue-700 dark:text-blue-300 mb-1">To use the terminal:</div>
                <div className="text-blue-600 dark:text-blue-400 space-y-1">
                  <div>1. Stop any running <code className="bg-blue-500/20 px-1 rounded">npm run dev</code> (Ctrl+C)</div>
                  <div>2. Run: <code className="bg-blue-500/20 px-1 rounded">npm run electron:dev</code></div>
                  <div>3. Wait for Electron window to open</div>
                </div>
              </div>
              <div className="text-xs bg-yellow-500/10 border border-yellow-500/20 rounded p-2 mb-3">
                <div className="font-medium text-yellow-700 dark:text-yellow-300 mb-1">If Electron won't start:</div>
                <div className="text-yellow-600 dark:text-yellow-400 space-y-1">
                  <div>Run: <code className="bg-yellow-500/20 px-1 rounded">npm run fix:terminal</code></div>
                  <div>Then: <code className="bg-yellow-500/20 px-1 rounded">npm run electron:dev</code></div>
                </div>
              </div>
              <div className="text-xs text-muted-foreground/50">
                Current mode: {navigator.userAgent.includes('Electron') ? 'Electron' : 'Browser'}
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <div className="text-sm mb-1 text-red-500">Terminal Error</div>
              <div className="text-xs text-muted-foreground/70 mb-2">
                {error}
              </div>
              <button
                onClick={resetAndRetry}
                className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Retry
              </button>
            </div>
          </div>
        ) : (
          <>
            {/* ✅ CRITICAL FIX: DOM Visibility & Tab Index (Required for keyboard focus) */}
            <div
              ref={terminalRef}
              id="xterm-container"
              className="absolute inset-0 p-2"
              style={{
                width: '100%',
                height: '100%',
                zIndex: 9999,
                position: 'relative',
                background: 'black',
                outline: 'none',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
                visibility: isLoading ? 'hidden' : 'visible',
                pointerEvents: isLoading ? 'none' : 'auto'
              }}
              tabIndex={0} // 👈 This is **required** for keyboard focus!
              onClick={() => {
                console.log(`🖱️ [TerminalPanel] Container clicked - forcing focus`)
                terminalRef.current?.focus()
                if (terminalInstanceRef.current && !isLoading) {
                  terminalInstanceRef.current.focus()
                  console.log(`🎯 [TerminalPanel] Terminal focused via click`)
                }
              }}
            />
            {/* ✅ Show loading overlay when needed */}
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
                <div className="text-center">
                  <div className="text-sm mb-1">Loading terminal...</div>
                  <div className="text-xs text-muted-foreground/70">
                    Initializing xterm.js components
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
